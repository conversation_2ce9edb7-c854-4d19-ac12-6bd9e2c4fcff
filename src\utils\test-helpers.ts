/**
 * Test Yardımcı Fonksiyonları
 * 
 * Bu dosya, unit testlerde dependency injection için
 * mock'lar ve test utility'leri <PERSON>.
 */

import type { InjectionKey } from 'vue';

/**
 * Test için mock provider oluşturur
 * 
 * @example
 * ```typescript
 * const mockProviders = createMockProviders({
 *   [configServiceKey]: mockConfigService,
 *   [loggerKey]: mockLogger
 * });
 * 
 * // Test setup'ında
 * global: {
 *   provide: mockProviders
 * }
 * ```
 */
export function createMockProviders(mocks: Record<symbol, unknown>) {
  return Object.fromEntries(
    Object.entries(mocks).map(([key, value]) => [key, value])
  );
}

/**
 * Mock ConfigService oluşturur
 * Partial implementasyon sağlar, sadece test edilen metodları override etmek yeterli
 */
export function createMockConfigService(overrides: Partial<any> = {}) {
  return {
    getAppName: () => 'Test App',
    getAppVersion: () => '1.0.0-test',
    getDataSourceType: () => 'mock' as const,
    getApiBaseUrl: () => 'http://localhost:3000/api',
    getApiTimeout: () => 5000,
    getLogLevel: () => 'debug' as const,
    getDefaultLocale: () => 'en-US',
    getFallbackLocale: () => 'en-US',
    getDefaultTheme: () => 'light' as const,
    getNotificationDisplayLevel: () => 'info' as const,
    getApiErrorNotificationType: () => 'friendly' as const,
    getNotificationDefaultPosition: () => 'top-right' as const,
    getNotificationDefaultTimeout: () => 3000,
    getNotificationDefaultGroup: () => true,
    getNotificationDefaultCloseBtnLabel: () => '',
    isDevelopment: () => true,
    isProduction: () => false,
    ...overrides
  };
}

/**
 * Mock Logger oluşturur
 */
export function createMockLogger(overrides: Partial<any> = {}) {
  return {
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    debug: jest.fn(),
    trace: jest.fn(),
    ...overrides
  };
}

/**
 * Mock DataService oluşturur
 */
export function createMockDataService(overrides: Partial<any> = {}) {
  return {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    ...overrides
  };
}

/**
 * Mock NotificationService oluşturur
 */
export function createMockNotificationService(overrides: Partial<any> = {}) {
  return {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    info: jest.fn(),
    ...overrides
  };
}

/**
 * Vue Test Utils için global config oluşturur
 * 
 * @example
 * ```typescript
 * const globalConfig = createTestGlobalConfig({
 *   configService: createMockConfigService(),
 *   logger: createMockLogger()
 * });
 * 
 * mount(Component, { global: globalConfig });
 * ```
 */
export function createTestGlobalConfig(services: {
  configService?: any;
  logger?: any;
  dataService?: any;
  notificationService?: any;
  themeService?: any;
}) {
  // Import'lar test zamanında yapılacak
  // const { configServiceKey, loggerKey, ... } = await import('src/constants/keys');
  
  const provide: Record<symbol, unknown> = {};
  
  // Servisler varsa provide et
  if (services.configService) {
    // provide[configServiceKey as symbol] = services.configService;
  }
  
  if (services.logger) {
    // provide[loggerKey as symbol] = services.logger;
  }
  
  // Diğer servisler...
  
  return {
    provide,
    // Diğer global config'ler (plugins, directives, vb.)
  };
}

/**
 * Test için environment değişkenlerini mock'lar
 */
export function mockEnvironment(env: Partial<ImportMetaEnv>) {
  const originalEnv = import.meta.env;
  
  // Environment'ı mock'la
  Object.defineProperty(import.meta, 'env', {
    value: { ...originalEnv, ...env },
    writable: true
  });
  
  // Cleanup fonksiyonu döndür
  return () => {
    Object.defineProperty(import.meta, 'env', {
      value: originalEnv,
      writable: true
    });
  };
}

/**
 * Async injection için test helper'ı
 * Vue composition API'de inject'in async context'te kullanımı için
 */
export async function withInjection<T>(
  key: InjectionKey<T>,
  value: T,
  callback: () => Promise<void> | void
) {
  // Bu fonksiyon test framework'üne göre implement edilecek
  // Jest, Vitest vb. için farklı implementasyonlar olabilir
  await callback();
}

/**
 * Test için genel setup fonksiyonu
 * Her test dosyasında kullanılabilir
 */
export function setupTestEnvironment() {
  // Global test setup'ları
  // Mock'lar, spy'lar vb.
  
  beforeEach(() => {
    // Her test öncesi cleanup
    jest.clearAllMocks();
  });
  
  afterEach(() => {
    // Her test sonrası cleanup
  });
}

/**
 * Component test için wrapper
 * Dependency injection ile component test etmek için
 */
export function createComponentTestWrapper(
  component: any,
  options: {
    services?: Record<string, any>;
    props?: Record<string, any>;
    slots?: Record<string, any>;
  } = {}
) {
  const { services = {}, props = {}, slots = {} } = options;
  
  const globalConfig = createTestGlobalConfig(services);
  
  // Bu fonksiyon Vue Test Utils'e göre implement edilecek
  // return mount(component, {
  //   props,
  //   slots,
  //   global: globalConfig
  // });
}

// Type helpers for better TypeScript support in tests
export type MockFunction<T extends (...args: any[]) => any> = jest.MockedFunction<T>;
export type MockObject<T> = { [K in keyof T]: T[K] extends (...args: any[]) => any ? MockFunction<T[K]> : T[K] };

/**
 * Type-safe mock oluşturur
 */
export function createTypedMock<T>(): MockObject<T> {
  return {} as MockObject<T>;
}
