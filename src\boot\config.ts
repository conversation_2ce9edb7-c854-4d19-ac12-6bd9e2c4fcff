/**
 * Configuration Service Boot File
 *
 * Bu boot dosyası ConfigService'i oluşturur ve global olarak sağlar.
 * <PERSON>ğer tüm boot dosyalarından önce yüklenmesi gerekir çünkü
 * diğer servisler ConfigService'e bağımlıdır.
 *
 * Sorumluluklar:
 * - ConfigService instance'ı oluşturma
 * - Global provide ile uygulamaya sağlama
 * - Konfigürasyon validasyonu
 * - Development modda debug bilgileri
 */

import { boot } from 'quasar/wrappers';
import { ConfigService } from 'src/core/config/ConfigService';
import { configServiceKey } from 'src/constants/keys';

/**
 * ConfigService Boot Function
 *
 * SOLID Prensiplerine uygun:
 * - Single Responsibility: Sadece ConfigService'i provide etme
 * - Dependency Inversion: Interface'e bağlı provide
 */
export default boot(({ app }) => {
  // ConfigService instance'ı oluştur
  const configService = new ConfigService();

  // Development modda konfigürasyon bilgilerini göster
  if (configService.isDevelopment()) {
    console.group('🔧 ConfigService Initialized');
    console.log('📋 Configuration Summary:', configService.getConfigSummary());

    // Validation hatalarını göster
    const errors = configService.getValidationErrors();
    if (errors.length > 0) {
      console.group('❌ Configuration Errors');
      errors.forEach((error) => console.error(error));
      console.groupEnd();
    } else {
      console.log('✅ Configuration is valid');
    }

    console.groupEnd();
  }

  // Production modda kritik hataları kontrol et
  if (configService.isProduction() && !configService.isValid()) {
    const errors = configService.getValidationErrors();
    console.error('🚨 Critical configuration errors in production:', errors);

    // Production'da kritik hatalar varsa uygulamayı durdur
    throw new Error(`Configuration validation failed: ${errors.join(', ')}`);
  }

  // ConfigService'i global olarak provide et
  app.provide(configServiceKey, configService);

  // Development modda global window objesine ekle (debugging için)
  if (configService.isDevelopment()) {
    (window as Record<string, unknown>).__CONFIG_SERVICE__ = configService;
    console.log('🔍 ConfigService available at window.__CONFIG_SERVICE__ for debugging');
  }
});

/**
 * Boot dosyası sıralaması:
 *
 * 1. config.ts (bu dosya) - İlk sırada olmalı
 * 2. logger.ts - ConfigService'e bağımlı
 * 3. notify-defaults.ts - ConfigService'e bağımlı
 * 4. axios.ts - ConfigService'e bağımlı
 * 5. firebase.ts - ConfigService'e bağımlı
 * 6. data-service.ts - Tüm yukarıdakilere bağımlı
 * 7. i18n.ts - ConfigService'e bağımlı
 * 8. theme.ts - ConfigService'e bağımlı
 *
 * quasar.config.js'de:
 * boot: [
 *   'config',
 *   'logger',
 *   'notify-defaults',
 *   'axios',
 *   'firebase',
 *   'data-service',
 *   'i18n',
 *   'theme'
 * ]
 */
