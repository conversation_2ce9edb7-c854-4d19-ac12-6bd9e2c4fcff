# Proje Adı: [Projenizin Adını Buraya Yazın] - Modüler Quasar Uygulaması (Detaylı Plan)

Bu TODO listesi, Quasar Framework ve TypeScript kullanarak SOLID prensiplerine uygun, m<PERSON><PERSON><PERSON>, ç<PERSON><PERSON> dil, ç<PERSON><PERSON> tema, yapılandırılabilir loglama/notifikasyon ve kimlik doğrulama özelliklerine sahip bir web uygulaması geliştirmek için adım adım bir yol haritası sunar.

## 0. Hazırlık ve Ortam Kurulumu

- [x] **Node.js ve Yarn/NPM Kurulumu:** Güncel LTS versiyonlarının kurulu olduğundan emin olun.
- [x] **Quasar CLI Kurulumu:** `yarn global add @quasar/cli` veya `npm install -g @quasar/cli`.
- [x] **IDE Kurulumu ve Eklentiler:**
  - [x] VS Code (önerilir).
  - [x] Volar (Vue 3 için resmi eklenti) + TypeScript Vue Plugin (Volar).
  - [x] ESLint eklentisi.
  - [x] Prettier - Code formatter eklentisi.
  - [x] Quasar Framework (Official) eklentisi (VS Code için).
- [x] **Git Kurulumu ve Proje için Repository Oluşturma:**
  - [x] `git init`
  - [x] Uzak bir repository (GitHub, GitLab vb.) oluşturun ve projeyi bağlayın.
  - [x] `.gitignore` dosyası oluşturun (Quasar CLI genelde bunu yapar).

## 1. Proje Başlatma ve Temel Çevre Yapılandırması

- [x] **Quasar Projesi Oluşturma:**
  - `quasar create <proje-klasor-adi>` komutu çalıştırıldı.
  - Seçimler: Quasar CLI with Vite, TypeScript, Quasar v2 (Vue 3), SCSS, ESLint+Prettier, Pinia, Axios, Vue-i18n.
- [ ] **`.env` Ortam Değişkenleri Yönetimi:**
  - [ ] **`.env.example` Dosyası Oluşturma ve Yapılandırma:**
    - **Konum:** Proje kök dizini.
    - **İçerik:**
      - `VITE_APP_NAME="Benim Modüler Quasar Uygulamam"`
      - `VITE_APP_VERSION="1.0.0"`
      - `VITE_DATA_SOURCE_TYPE="firebase"` (Olası değerler: "firebase", "restapi", "mock")
      - **REST API Ayarları (Eğer `VITE_DATA_SOURCE_TYPE="restapi"`):**
        - `VITE_API_BASE_URL="http://localhost:8080/api"`
        - `VITE_API_TIMEOUT="30000"`
      - **Firebase Ayarları (Eğer `VITE_DATA_SOURCE_TYPE="firebase"`):**
        - `VITE_FIREBASE_API_KEY="YOUR_FIREBASE_API_KEY_PLACEHOLDER"`
        - `VITE_FIREBASE_AUTH_DOMAIN="YOUR_FIREBASE_AUTH_DOMAIN_PLACEHOLDER"`
        - `VITE_FIREBASE_PROJECT_ID="YOUR_FIREBASE_PROJECT_ID_PLACEHOLDER"`
        - `VITE_FIREBASE_STORAGE_BUCKET="YOUR_FIREBASE_STORAGE_BUCKET_PLACEHOLDER"`
        - `VITE_FIREBASE_MESSAGING_SENDER_ID="YOUR_FIREBASE_MESSAGING_SENDER_ID_PLACEHOLDER"`
        - `VITE_FIREBASE_APP_ID="YOUR_FIREBASE_APP_ID_PLACEHOLDER"`
        - `VITE_FIREBASE_MEASUREMENT_ID="YOUR_FIREBASE_MEASUREMENT_ID_PLACEHOLDER"` (Opsiyonel)
      - **i18n Ayarları:**
        - `VITE_DEFAULT_LOCALE="tr-TR"`
        - `VITE_FALLBACK_LOCALE="en-US"`
      - **Tema Ayarları:**
        - `VITE_DEFAULT_THEME="light"` (Olası değerler: "light", "dark", "auto")
      - **Loglama Ayarları:**
        - `VITE_LOG_LEVEL="debug"` (Olası değerler: "error", "warn", "info", "debug", "trace")
      - **Notifikasyon Ayarları (Quasar Notify):**
        - `VITE_NOTIFICATION_DISPLAY_LEVEL="info"` (Olası değerler: "error", "warn", "info", "success", "none")
        - `VITE_API_ERROR_NOTIFICATION_TYPE="friendly"` (Olası değerler: "toast", "friendly", "silent")
        - `VITE_NOTIFICATION_DEFAULT_POSITION="top-right"` (Quasar Notify pozisyonları)
        - `VITE_NOTIFICATION_DEFAULT_TIMEOUT="5000"` (milisaniye)
        - `VITE_NOTIFICATION_DEFAULT_GROUP="true"` ("true" | "false")
        - `VITE_NOTIFICATION_DEFAULT_CLOSE_BTN_LABEL=""` (Boş = Quasar varsayılanı, veya ikon/metin)
    - **Git:** Commit edilecek.
  - [ ] **`.gitignore` Dosyasını Gözden Geçirme:**
    - `.env`, `.env.*`, `!.env.example` kurallarının doğruluğundan emin olun.
    - `.env.development`, `.env.production` gibi dosyaların da (hassas bilgi içerebilecekleri ve CI/CD üzerinden yönetilmeleri daha iyi olabileceği için) ignore edilmesi önerilir.
  - [ ] **Lokal Geliştirme İçin `.env.local` Dosyası:**
    - `.env.example` dosyasını kopyalayıp proje kök dizininde `.env.local` olarak adlandırın.
    - `.env.local` dosyasına kendi lokal geliştirme ortamınız için gerçek değerleri girin.
    - **Git:** Ignore edilecek.
  - [ ] **TypeScript Ortam Değişkeni Tanımları (`src/vite-env.d.ts`):**
    - **Sorumluluk:** `import.meta.env` için tip güvenliği sağlamak. Bu dosya **sadece** `ConfigService` tarafından doğrudan okunacak `import.meta.env` değişkenlerinin ham tiplerini tanımlayacak. `ConfigService` daha sonra bu ham değerleri işleyip, daha kullanışlı tiplerle (örn: string yerine number, boolean) sunacak.
    - **İçerik:** `ImportMetaEnv` arayüzü, `.env.example` dosyasındaki tüm `VITE_` değişkenlerini ve bunların ham (genellikle string) tiplerini içerecek şekilde tanımlayın.
- [ ] **ESLint ve Prettier Entegrasyonunu Kontrol Etme.**
- [ ] **Projenin Çalıştığını Doğrulama (`quasar dev`).**

## 2. Temel Dizin Yapısı ve Modülerlik Anlayışı

- [ ] **Ana Dizin Yapısını Oluşturma/Gözden Geçirme (`src/`):**
  - `assets/`
  - `boot/`
  - `components/` (Genel/Paylaşılan Mikro UI Bileşenleri)
  - `composables/` (Genel/Paylaşılan Vue Composition API Fonksiyonları)
  - `constants/` (Uygulama geneli sabitler, `InjectionKey`'ler için `keys.ts` de burada olabilir)
  - `core/`
    - `config/`
      - `IConfigService.ts`
      - `ConfigService.ts`
    - `data-layer/`
      - `implementations/`
        - `firebase/`
        - `rest/`
        - `mock/`
    - `services/`
      - `logger/`
      - `notification/`
    - `theme/`
    - `types/` (Uygulama geneli TypeScript tanımları)
  - `css/`
  - `i18n/`
    - `en-US/`
    - `tr-TR/`
  - `layouts/`
  - `modules/`
  - `pages/`
  - `router/`
  - `stores/` (Genel/Paylaşılan Pinia Store'ları)
  - `utils/` (Genel yardımcı fonksiyonlar)
- [ ] **Modül Yapısı Tanımı (`src/modules/<module-name>/`):**
  - `components/`
  - `composables/`
  - `constants/`
  - `pages/`
  - `router/` (`index.ts` veya `routes.ts` -> `RouteRecordRaw[]` export eder)
  - `services/`
  - `stores/`
  - `types/`
  - `utils/`
  - `i18n/` (`en-US.ts`, `tr-TR.ts` -> çeviri objesi export eder)
  - `index.ts` (Opsiyonel, modülün public API'si için)

## 3. Bağımlılık Yönetimi (Dependency Injection - DI)

Bu bölümde, uygulama genelinde bağımlılıkların nasıl yönetileceği ve enjekte edileceği açıklanmaktadır. Amacımız, bileşenleri ve servisleri gevşek bağlı (loosely coupled) ve test edilebilir tutmaktır. Vue 3'ün `provide`/`inject` API'si ve Pinia store'ları ana DI mekanizmalarımız olacaktır.

- [ ] **DI Stratejisinin Belirlenmesi:**
  - [ ] **Vue `provide`/`inject` Kullanımı:**
    - **Senaryolar:** Uygulama genelinde veya belirli bir bileşen ağacında paylaşılması gereken servis örnekleri, global konfigürasyonlar.
    - **Sağlama Yeri:** Genellikle Quasar boot dosyalarında `app.provide('key', instance)` ile.
    - **Enjekte Etme Yeri:** Bileşenlerin `setup()` veya composable'lar içinde `inject('key')` ile.
  - [ ] **Pinia Store'larının Kullanımı:**
    - **Senaryolar:** State yönetimi, action'lar, getter'lar.
  - [ ] **Constructor Injection (Servisler İçin):**
    - **Senaryolar:** Bir servisin başka bir servise bağımlılığı.
    - **Yaklaşım:** Bağımlılıklar constructor'a parametre olarak geçirilir.
  - [ ] **Doğrudan Import (Singleton Servisler/Utility'ler için):**
    - **Senaryolar:** State içermeyen utility fonksiyonları, basit singleton servisler.
    - **Dikkat:** Test edilebilirliği zorlaştırabilir, dikkatli kullanılmalı.
- [ ] **`InjectionKey` Kullanımı (Tip Güvenliği için):**

  - [ ] `provide` ve `inject` için `InjectionKey<T>` kullanılacaktır.
  - [ ] `src/constants/keys.ts` (veya ilgili servisin/modülün yanında) `InjectionKey`'ler tanımlanacaktır.

    ```typescript
    // src/constants/keys.ts (Örnek)
    import { InjectionKey } from 'vue';
    import { IConfigService } from 'src/core/config/IConfigService';
    import { IDataService } from 'src/core/data-layer/IDataService';
    import { ILogger } from 'src/core/services/logger/ILogger';
    // import { INotificationService } from 'src/core/services/notification/INotificationService'; // Eğer arayüzü varsa

    export const configServiceKey: InjectionKey<IConfigService> = Symbol('ConfigService');
    export const dataServiceKey: InjectionKey<IDataService> = Symbol('DataService');
    export const loggerKey: InjectionKey<ILogger> = Symbol('Logger');
    // export const notificationServiceKey: InjectionKey<INotificationService> = Symbol('NotificationService');
    // ...vb.
    ```

- [ ] **Servislerin Oluşturulması ve Sağlanması:**
  - [ ] **`src/boot/` dizinindeki boot dosyaları:** Ana servislerin örneklerini oluşturup `app.provide(key, instance)` ile global olarak sağlayacaktır.
- [ ] **Bağımlılıkların Test Edilmesi:**
  - [ ] Birim testlerinde bağımlılıklar mock edilebilmelidir (`global: { provide: { ... } }`).
- [ ] **DI Prensiplerinin Gözden Geçirilmesi:**
  - [ ] **Bağımlılıkların Tersine Çevrilmesi Prensibi (DIP).**
  - [ ] **Gevşek Bağlılık (Loose Coupling).**

## 4. Çekirdek Altyapı ve Servislerin Kurulumu

### 4.0. Yapılandırma Servisi (Config Service)

- [ ] **`src/core/config/IConfigService.ts` Arayüzü Tanımlama:**
  - **Sorumluluk:** Uygulamanın ihtiyaç duyduğu tüm yapılandırma değerlerini (işlenmiş ve tip güvenli) getiren metodları tanımlar.
  - **Örnek Metodlar:**
    - `getAppName(): string;`
    - `getAppVersion(): string;`
    - `getDataSourceType(): 'firebase' | 'restapi' | 'mock';`
    - `getApiBaseUrl(): string | undefined;`
    - `getApiTimeout(): number | undefined;`
    - `getFirebaseConfig(): YourFirebaseConfigType | undefined;` // YourFirebaseConfigType ayrı bir tip olmalı
    - `getLogLevel(): 'error' | 'warn' | 'info' | 'debug' | 'trace';`
    - `getDefaultLocale(): string;`
    - `getFallbackLocale(): string;`
    - `getDefaultTheme(): 'light' | 'dark' | 'auto';`
    - `getNotificationDisplayLevel(): 'error' | 'warn' | 'info' | 'success' | 'none';`
    - `getApiErrorNotificationType(): 'toast' | 'friendly' | 'silent';`
    - `getNotificationDefaultPosition(): QNotifyPosition;` // QNotifyPosition tipini tanımla
    - `getNotificationDefaultTimeout(): number;`
    - `getNotificationDefaultGroup(): boolean;`
    - `getNotificationDefaultCloseBtnLabel(): string | boolean;`
    - `isDevelopment(): boolean;`
    - `isProduction(): boolean;`
- [ ] **`src/core/config/ConfigService.ts` Implementasyonu:**
  - **Sorumluluk:** `IConfigService` arayüzünü implemente eder.
  - `import.meta.env`'den ham değerleri okur.
  - Bu değerleri işler (örn: string'den number'a/boolean'a çevirme, varsayılan değer atama).
  - İşlenmiş değerleri arayüz metodları aracılığıyla sunar.
  - **Import Ettiği:** `IConfigService`, `import.meta.env` (tip için `src/vite-env.d.ts`).
- [ ] **`src/boot/config.ts` Boot Dosyası:**
  - **Sorumluluk:** `ConfigService` örneğini oluşturur ve `app.provide(configServiceKey, instance)` ile global olarak sağlar.
  - **`quasar.config.js`'e Ekleme:** `boot: ['config', /* diğerleri... */]` (Diğer boot dosyalarından önce yüklenmesi genellikle iyidir).

### 4.1. Logger Servisi

- [ ] **`src/core/services/logger/ILogger.ts` Arayüzü Tanımlama.**
- [ ] **`src/core/services/logger/LoggerService.ts` Implementasyonu:**
  - Constructor'ında `IConfigService`'i enjekte alır (`inject(configServiceKey)`).
  - `configService.getLogLevel()` metodunu kullanarak log seviyesini alır ve logları filtreler.
- [ ] **`src/boot/logger.ts` Boot Dosyası:**
  - `ConfigService`'i (`inject(configServiceKey)`) alır.
  - `LoggerService` örneğini `ConfigService` ile oluşturur.
  - `app.provide(loggerKey, loggerInstance)` ile sağlar.
  - `quasar.config.js`'e eklenir ( `config.ts`'den sonra).

### 4.2. Quasar Notify Plugin Yapılandırması ve Notifikasyon Servisi

- [ ] **Quasar Notify Plugin Kurulum Kontrolü:** `quasar.config.js` -> `framework.plugins: ['Notify']`.
- [ ] **`src/boot/notify-defaults.ts` Boot Dosyası:**
  - `ConfigService`'i (`inject(configServiceKey)`) enjekte alır.
  - `configService` üzerinden `getNotificationDefaultPosition()`, `getNotificationDefaultTimeout()` vb. ayarları okur.
  - `Notify.setDefaults({})` uygular.
  - `quasar.config.js`'e eklenir ( `config.ts`'den sonra).
- [ ] **`src/core/services/notification/NotificationService.ts` (veya `src/composables/useNotify.ts`):**
  - Quasar `Notify`'yi sarmalayan fonksiyonlar sunar.
  - `ConfigService`'i (`inject(configServiceKey)`) enjekte alarak `getNotificationDisplayLevel()` ve `getApiErrorNotificationType()`'a göre davranır.
  - (Opsiyonel) Bu servis de `app.provide(notificationServiceKey, instance)` ile sağlanabilir veya doğrudan import edilebilir.

### 4.3. Çekirdek Veri Katmanı (`Core Data Layer`)

- [ ] **`src/core/data-layer/IDataService.ts` Arayüzü Tanımlama.**
- [ ] **`src/core/data-layer/implementations/firebase/firebaseDataService.ts` Implementasyonu:**
  - (Opsiyonel) `ConfigService`'i (`inject(configServiceKey)`) enjekte alarak Firebase yapılandırmasını alabilir.
- [ ] **`src/boot/firebase.ts` (Firebase Başlatma):**
  - `ConfigService`'i (`inject(configServiceKey)`) enjekte alır.
  - `configService.getDataSourceType()` ve `configService.getFirebaseConfig()` kullanarak Firebase'i koşullu olarak başlatır.
  - `quasar.config.js`'e eklenir ( `config.ts`'den sonra).
- [ ] **`src/core/data-layer/implementations/rest/restApiService.ts` Implementasyonu.**
- [ ] **`src/boot/axios.ts` (Axios Yapılandırması):**
  - `ConfigService`'i (`inject(configServiceKey)`) enjekte alır.
  - `configService.getApiBaseUrl()`, `configService.getApiTimeout()` gibi ayarları kullanarak Axios instance'ını yapılandırır.
  - `axiosInstance` export eder.
  - `quasar.config.js`'e eklenir ( `config.ts`'den sonra).
- [ ] **`src/core/data-layer/implementations/mock/mockDataService.ts` (Opsiyonel) Implementasyonu.**
- [ ] **`src/core/data-layer/dataServiceProvider.ts` (Factory):**
  - `ConfigService`'i (`inject(configServiceKey)`) enjekte alır.
  - `configService.getDataSourceType()`'a göre uygun `IDataService` implementasyonunu döndürür.
- [ ] **`src/boot/data-service.ts` (Veri Servisini Enjekte Etme):**
  - `ConfigService`'i (`inject(configServiceKey)`) enjekte alır.
  - `dataServiceProvider`'ı (veya onun `getDataService` metodunu) `ConfigService` ile çağırır.
  - `IDataService` örneğini `app.provide(dataServiceKey, instance)` ile sağlar.
  - `quasar.config.js`'e eklenir ( `config.ts` ve diğer bağımlı boot'lardan sonra).

## 5. Çekirdek Modüller ve Özellikler

### 5.1. Çoklu Dil (i18n) Yapılandırması

- [ ] **`src/i18n/index.ts` (Ana i18n Yapılandırma Dosyası).**
- [ ] **Genel Dil Dosyaları (örn: `src/i18n/en-US/index.ts`).**
- [ ] **`src/boot/i18n.ts` (Vue-i18n Başlatma):**
  - `ConfigService`'i (`inject(configServiceKey)`) enjekte alır.
  - `configService.getDefaultLocale()`, `configService.getFallbackLocale()` ayarlarını kullanır.
  - `quasar.config.js`'e eklenir ( `config.ts`'den sonra).
- [ ] **Dil Değiştirme Bileşeni (`src/components/LanguageSwitcher.vue`).**

### 5.2. Çoklu Tema (Theme) Yönetimi

- [ ] **`src/core/theme/theme.service.ts` (veya `src/composables/useTheme.ts`):**
  - `ConfigService`'i (`inject(configServiceKey)`) enjekte alır.
  - `configService.getDefaultTheme()` kullanır.
- [ ] **`src/stores/theme.store.ts` (Pinia Store - Opsiyonel).**
- [ ] **`src/boot/theme.ts`:** Başlangıç temasını yükler (`ConfigService`'i kullanan `theme.service` aracılığıyla).
  - `quasar.config.js`'e eklenir ( `config.ts`'den sonra).
- [ ] **Tema Değiştirme Bileşeni (`src/components/ThemeSwitcher.vue`).**
- [ ] **Quasar Tema Yapılandırması (`src/css/quasar.variables.scss`).**

### 5.3. Kimlik Doğrulama (Auth) Modülü (`src/modules/auth/`)

- [ ] **Dizin Yapısı.**
- [ ] **`types/IUser.ts`, `types/AuthCredentials.ts` vb.**
- [ ] **`services/AuthService.ts`:** `IDataService`'i (`inject(dataServiceKey)`) ve `ILogger`'ı (`inject(loggerKey)`) kullanır. (Bu servisler zaten `ConfigService`'i dolaylı olarak kullanıyor olacak).
- [ ] **`stores/auth.store.ts` (Pinia Store):** `AuthService`'i kullanır.
- [ ] **`pages/LoginPage.vue`, `RegisterPage.vue` vb.**
- [ ] **`router/index.ts` (Auth Modülü Rotaları).**
- [ ] **`i18n/en-US.ts`, `tr-TR.ts`.**
- [ ] **Auth Store `autoLogin` action'ı uygulama başlangıcında çağrılır.**

## 6. Ana Router ve Navigasyon Korumaları

- [ ] **`src/router/routes.ts` (Ana Rota Tanımları).**
- [ ] **`src/router/index.ts` (Vue Router Instance):** Navigasyon korumaları (`beforeEach`), `useAuthStore` kullanır.

## 7. Örnek Bir Özellik Modülü (Örn: Todo List - `src/modules/todo/`)

- [ ] **Dizin Yapısı.**
- [ ] **`types/ITodo.ts`.**
- [ ] **`services/TodoService.ts`:** `IDataService`'i (`inject(dataServiceKey)`) kullanır.
- [ ] **`stores/todo.store.ts` (Pinia Store):** `TodoService`'i kullanır.
- [ ] **`components/TodoList.vue`, `TodoItem.vue`, `AddTodoForm.vue`.**
- [ ] **`pages/TodoPage.vue`:** `meta: { requiresAuth: true }` olabilir.
- [ ] **`router/index.ts`.**
- [ ] **`i18n/en-US.ts`, `tr-TR.ts`.**
- [ ] **Ana Router'a Entegrasyon.**

## 8. SOLID Prensipleri, Mikro Yaklaşımlar ve Kod Tekrarı Üzerine Sürekli Dikkat

- [ ] Tek Sorumluluk Prensibi (SRP)
- [ ] Açık/Kapalı Prensibi (OCP)
- [ ] Liskov Yerine Geçme Prensibi (LSP)
- [ ] Arayüz Ayrımı Prensibi (ISP)
- [ ] **Bağımlılıkların Tersine Çevrilmesi Prensibi (DIP):** Yüksek seviyeli modüller/sınıflar, düşük seviyeli modüllere/sınıflara değil, soyutlamalara (arayüzler, `InjectionKey`'ler) bağımlı olmalıdır. Örneğin, servislerin `IConfigService`'e (`configServiceKey` ile) veya `IDataService`'e (`dataServiceKey` ile) bağımlı olması.
- [ ] Kod Tekrarından Kaçınma (DRY)
- [ ] Mikro Komponentler/Servisler/Arayüzler

## 9. Test ve Kalite Güvencesi

- [ ] **Birim Testleri (Vitest/Jest):** `ConfigService` dahil tüm servisler, store'lar, composable'lar kolayca mock edilebilir.
- [ ] **Bileşen Testleri (Quasar Testing Harness).**
- [ ] **Uçtan Uca Testler (E2E Tests - Cypress/Playwright).**
- [ ] **Linting ve Formatlama (Husky + lint-staged ile pre-commit hook'ları).**

## 10. Dokümantasyon

- [ ] **README.md (Proje Geneli).**
- [ ] **Modül Bazlı README dosyaları.**
- [ ] **Kod İçi Yorumlar (JSDoc/TSDoc).**
- [ ] **Mimari Kararların Dokümantasyonu (ADR - Architecture Decision Records - Opsiyonel).**

## 11. Sürekli Geliştirme ve İyileştirmeler

- [ ] Düzenli Code Review'lar.
- [ ] Iteratif Geliştirme.
- [ ] Refactoring.
- [ ] Performans Optimizasyonları.
- [ ] Erişilebilirlik (a11y) Kontrolleri.
